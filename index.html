<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signal Framework</title>
</head>
<body>
    
<div id="app"></div>

<template>
    <div @store="{ count: 0 }">
        <h1>{count}</h1>
        <button onclick="increment()">Increment</button>
    </div>

    <div @users="{ users: ['jhon' , '<PERSON><PERSON>' , '<PERSON><PERSON>' , '<PERSON><PERSON><PERSON>' ] }">
        {#each users as user , index}
            <p>{user} , {index}</p>
        {/each}
    </div>

    <div @lists="{items: ['Item 1', 'Item 2', 'Item 3']}">
        <h3>Items with Index:</h3>
        {#each items as item , index}
            <p>{item}</p>
            <p>{index}</p>
        {/each}
    </div>

    <div @data="{ users2: ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'], title: 'User List' }">
    <h3>{title}</h3>
    {#each users2 as user}
        <p>{user}</p>
    {/each}
    </div>
   
    <div @store="{isOpen: false  , isPending: true }">
        <button onclick="toggle()">Toggle</button>
        {#if isOpen}
            <p>Content is visible</p>
            {#else if isPending}
            <p>Loading...</p>
            {#else}
            <p>Content is hidden</p>
        {/if}
    </div>

    <div @store="{htmlContent : `<div>Hello World</div>`}">
         {@html htmlContent}
    </div>

    {new Date().toLocaleTimeString()}

    <div @store="{time: 'Loading...', autoCount: 0}">
        <h2>Auto-updating Time: {time}</h2>
        <h2>Auto-incrementing Counter: {autoCount}</h2>
    </div>

<div @store="{count: 5, users: ['John', 'Jane']}">
        <!-- Standalone expressions (auto-wrapped in span) -->
        {new Date().toLocaleTimeString()}
        {count * 2}
        {users.length > 0 ? 'Has users' : 'No users'}
        
        <!-- Inside tags -->
        <p>Current time: {new Date().toLocaleTimeString()}</p>
        <h1>Double count: {count * 2}</h1>
        <div>Math result: {Math.max(10, count)}</div>
        <span>Array join: {users.join(', ')}</span>
</div>

<div @lists="{items: ['Item 1', 'Item 2', 'Item 3']}">
    {#each items as item , index}
        <p>{item} , <span>{index}</span></p>
    {/each}
</div>

</template>
<script type="module">
import { createApp } from './app.js';

// Usage
const app = createApp({
    methods: {
        increment() {
            this.count++;
        },
        toggle() {
            this.isOpen = !this.isOpen;
        }
    },
    effects() {
        // Auto increment counter
        setInterval(() => {
            this.autoCount++;
        }, 1000);

        // Update time every second
        setInterval(() => {
            this.time = new Date().toLocaleTimeString();
        }, 1000);

        // One-time setup
        console.log('Component mounted with initial autoCount:', this.autoCount);
    }
});

app.mount('#app');
</script>
</body>
</html>