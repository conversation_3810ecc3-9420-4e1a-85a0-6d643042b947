<!DOCTYPE html>
<html>
<head>
    <title>Debug Regex</title>
</head>
<body>
    <script>
        // Test the regex pattern
        const eachRegex = /\{#each\s+(\w+)\s+as\s+(\w+)(?:,\s*(\w+))?\}([\s\S]*?)\{\/each\}/g;
        
        // Test cases
        const testCases = [
            '{#each items as item}\n    <p>{item}</p>\n{/each}',
            '{#each items as item, index}\n    <p>{item}</p>\n    <p>{index}</p>\n{/each}',
            '{#each items as item , index}\n    <p>{item}</p>\n    <p>{index}</p>\n{/each}'
        ];
        
        testCases.forEach((test, i) => {
            console.log(`Test ${i + 1}:`, test);
            const match = eachRegex.exec(test);
            if (match) {
                console.log('Match found:', {
                    fullMatch: match[0],
                    arrayName: match[1],
                    itemName: match[2],
                    indexName: match[3],
                    template: match[4]
                });
            } else {
                console.log('No match found');
            }
            eachRegex.lastIndex = 0; // Reset regex
        });
    </script>
</body>
</html>
